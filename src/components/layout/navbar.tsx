"use client"

import * as React from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { Menu } from "lucide-react"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ThemeToggle } from "@/components/theme-toggle"

const navigation = [
  { name: "Home", href: "/" },
  { name: "About Us", href: "/about" },
  { name: "Clubs", href: "/clubs" },
  { name: "Hackathons", href: "/hackathons" },
  { name: "Achievements", href: "/achievements" },
  { name: "Contact Us", href: "/contact" },
]

export function Navbar() {
  const [isOpen, setIsOpen] = React.useState(false)
  const pathname = usePathname()

  return (
    <header className="sticky top-0 z-50 w-full border-b border-gray-200 dark:border-gray-800 bg-white/95 dark:bg-gray-900/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-gray-900/60">
      <div className="container flex h-16 items-center justify-between px-4 sm:px-6 md:px-8">

        {/* Desktop Logo and Navigation */}
        <div className="hidden md:flex items-center space-x-8">
          <Link href="/" className="flex items-center space-x-3 touch-target">
            <Image
              src="/tech-logo.svg"
              alt="Tech@IITGN Logo"
              width={40}
              height={40}
              className="h-10 w-10 lg:h-12 lg:w-12 rounded-full"
            />
            <span className="font-bold font-space-grotesk text-base lg:text-lg">
              Technical Council
            </span>
          </Link>
          <nav className="flex items-center space-x-6 lg:space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "text-sm lg:text-base font-medium transition-colors hover:text-gray-900 dark:hover:text-gray-100 px-3 py-2 rounded-md touch-target",
                  pathname === item.href
                    ? "text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-800"
                    : "text-gray-600 dark:text-gray-400"
                )}
              >
                {item.name}
              </Link>
            ))}
          </nav>
        </div>

        {/* Mobile Layout */}
        <div className="flex md:hidden items-center justify-between w-full">
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 touch-target"
            onClick={() => setIsOpen(!isOpen)}
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">Toggle Menu</span>
          </Button>

          {/* Mobile logo */}
          <Link href="/" className="flex items-center space-x-2 touch-target">
            <Image
              src="/tech-logo.svg"
              alt="Tech@IITGN Logo"
              width={32}
              height={32}
              className="h-8 w-8 rounded-full"
            />
            <span className="font-bold font-space-grotesk text-sm">Technical Council</span>
          </Link>

          {/* Theme toggle */}
          <ThemeToggle />
        </div>

        {/* Desktop Theme Toggle */}
        <div className="hidden md:flex items-center">
          <ThemeToggle />
        </div>
      </div>

      {/* Mobile menu */}
      {isOpen && (
        <div className="fixed inset-0 top-16 z-50 bg-black/20 backdrop-blur-sm md:hidden">
          <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 shadow-lg">
            <div className="container px-4 py-6">
              <nav className="space-y-1">
                {navigation.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "block px-4 py-3 text-base font-medium rounded-lg transition-colors touch-target",
                      pathname === item.href
                        ? "text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-800"
                        : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800"
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}
              </nav>
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
